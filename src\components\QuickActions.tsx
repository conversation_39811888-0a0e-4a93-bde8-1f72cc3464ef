
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Package, Search, MapPin, Plus, Truck, Star } from "lucide-react";
import { <PERSON> } from "react-router-dom";

const QuickActions = () => {
  const actions = [
    {
      icon: Plus,
      label: "Book Parcel",
      description: "Send a new parcel",
      href: "/book-parcel",
      color: "bg-blue-600 hover:bg-blue-700"
    },
    {
      icon: Search,
      label: "Track Parcel",
      description: "Track your shipments",
      href: "/track-parcel",
      color: "bg-green-600 hover:bg-green-700"
    },
    {
      icon: MapPin,
      label: "Service Areas",
      description: "Check coverage",
      href: "/service-areas",
      color: "bg-purple-600 hover:bg-purple-700"
    },
    {
      icon: Truck,
      label: "Become Partner",
      description: "Join our delivery network",
      href: "/partner-register",
      color: "bg-orange-600 hover:bg-orange-700"
    }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Star className="h-5 w-5" />
          Quick Actions
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4">
          {actions.map((action) => (
            <Button
              key={action.label}
              asChild
              className={`${action.color} h-auto p-4 flex-col items-start text-left`}
            >
              <Link to={action.href}>
                <action.icon className="h-6 w-6 mb-2" />
                <div>
                  <p className="font-medium">{action.label}</p>
                  <p className="text-xs opacity-90">{action.description}</p>
                </div>
              </Link>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickActions;
