
import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Truck, Package, MapPin, Phone, User, FileText, CheckCircle } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const PartnerRegister = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState(1);
  const { user } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!user) {
      toast.error('Please login first to register as a partner');
      navigate('/auth');
      return;
    }

    setIsLoading(true);

    const formData = new FormData(e.currentTarget);
    const partnerData = {
      user_id: user.id,
      vehicle_type: formData.get('vehicle_type') as string,
      license_number: formData.get('license_number') as string,
      vehicle_number: formData.get('vehicle_number') as string,
      status: 'pending'
    };

    const { error } = await supabase
      .from('delivery_partners')
      .insert([partnerData]);

    if (error) {
      toast.error('Failed to submit application: ' + error.message);
    } else {
      toast.success('Application submitted successfully! We will review and get back to you soon.');
      setStep(3);
    }
    
    setIsLoading(false);
  };

  const vehicleTypes = [
    { value: 'bicycle', label: 'Bicycle', icon: '🚲' },
    { value: 'motorcycle', label: 'Motorcycle', icon: '🏍️' },
    { value: 'car', label: 'Car', icon: '🚗' },
    { value: 'van', label: 'Van', icon: '🚐' },
    { value: 'truck', label: 'Truck', icon: '🚛' }
  ];

  if (step === 3) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
        <Header />
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-md mx-auto text-center">
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="h-12 w-12 text-green-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Application Submitted!</h1>
            <p className="text-gray-600 mb-8">
              Thank you for your interest in becoming a delivery partner. We'll review your application and contact you within 2-3 business days.
            </p>
            <div className="space-y-3">
              <Button asChild className="w-full">
                <Link to="/dashboard">Go to Dashboard</Link>
              </Button>
              <Button variant="outline" asChild className="w-full">
                <Link to="/">Back to Home</Link>
              </Button>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Become a Delivery Partner
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Join our network of trusted delivery partners and earn flexible income
            </p>
            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Truck className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">Flexible Schedule</h3>
                <p className="text-gray-600">Work when you want, earn on your terms</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Package className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">Daily Earnings</h3>
                <p className="text-gray-600">Get paid daily for completed deliveries</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <MapPin className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="font-semibold mb-2">Local Area</h3>
                <p className="text-gray-600">Deliver in your neighborhood</p>
              </div>
            </div>
          </div>

          {/* Application Form */}
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle>Partner Application</CardTitle>
              <CardDescription>
                Fill out the form below to start your journey as a delivery partner
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!user ? (
                <div className="text-center py-8">
                  <User className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Login Required</h3>
                  <p className="text-gray-600 mb-6">
                    You need to be logged in to apply as a delivery partner
                  </p>
                  <Button asChild>
                    <Link to="/auth">Login / Sign Up</Link>
                  </Button>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="full_name">Full Name</Label>
                      <Input
                        id="full_name"
                        name="full_name"
                        defaultValue={user.user_metadata?.full_name || ''}
                        disabled
                        className="bg-gray-50"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        name="email"
                        defaultValue={user.email || ''}
                        disabled
                        className="bg-gray-50"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="vehicle_type">Vehicle Type</Label>
                    <Select name="vehicle_type" required>
                      <SelectTrigger>
                        <SelectValue placeholder="Select your vehicle type" />
                      </SelectTrigger>
                      <SelectContent>
                        {vehicleTypes.map((vehicle) => (
                          <SelectItem key={vehicle.value} value={vehicle.value}>
                            <span className="flex items-center gap-2">
                              <span>{vehicle.icon}</span>
                              {vehicle.label}
                            </span>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="license_number">
                        Driving License Number
                      </Label>
                      <div className="relative">
                        <FileText className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="license_number"
                          name="license_number"
                          placeholder="Enter license number"
                          className="pl-10"
                          required
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="vehicle_number">Vehicle Number</Label>
                      <div className="relative">
                        <Truck className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="vehicle_number"
                          name="vehicle_number"
                          placeholder="Enter vehicle number"
                          className="pl-10"
                          required
                        />
                      </div>
                    </div>
                  </div>

                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-blue-900 mb-2">Requirements:</h4>
                    <ul className="text-blue-800 text-sm space-y-1">
                      <li>• Valid driving license</li>
                      <li>• Vehicle registration documents</li>
                      <li>• Age 18 or above</li>
                      <li>• Valid phone number</li>
                    </ul>
                  </div>

                  <Button 
                    type="submit" 
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                    disabled={isLoading}
                  >
                    {isLoading ? 'Submitting Application...' : 'Submit Application'}
                  </Button>
                </form>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
      
      <Footer />
    </div>
  );
};

export default PartnerRegister;
