
import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { User } from "lucide-react";
import { Link } from "react-router-dom";

const LoginPrompt = () => {
  return (
    <Card className="max-w-md mx-auto">
      <CardContent className="text-center py-8">
        <User className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">Login Required</h3>
        <p className="text-gray-600 mb-6">
          You need to be logged in to book a parcel
        </p>
        <Button asChild>
          <Link to="/auth">Login / Sign Up</Link>
        </Button>
      </CardContent>
    </Card>
  );
};

export default LoginPrompt;
