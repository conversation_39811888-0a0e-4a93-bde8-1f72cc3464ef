
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { MapPin, Clock, Package } from "lucide-react";
import { useState } from "react";
import { toast } from "@/hooks/use-toast";
import Header from "@/components/Header";

const ServiceAreas = () => {
  const [pincode, setPincode] = useState("");
  const [serviceAvailable, setServiceAvailable] = useState(null);

  const serviceAreas = [
    { area: "Andheri West", pincode: "400058", deliveryTime: "2-3 hours" },
    { area: "Bandra West", pincode: "400050", deliveryTime: "1-2 hours" },
    { area: "Juhu", pincode: "400049", deliveryTime: "2-3 hours" },
    { area: "Santacruz West", pincode: "400054", deliveryTime: "2-3 hours" },
    { area: "Vile Parle", pincode: "400056", deliveryTime: "3-4 hours" },
    { area: "Powai", pincode: "400076", deliveryTime: "3-4 hours" },
    { area: "Malad West", pincode: "400064", deliveryTime: "4-5 hours" },
    { area: "Borivali West", pincode: "400092", deliveryTime: "4-5 hours" },
  ];

  const handleCheckService = (e: React.FormEvent) => {
    e.preventDefault();
    const area = serviceAreas.find(area => area.pincode === pincode);
    
    if (area) {
      setServiceAvailable(area);
      toast({
        title: "Service Available!",
        description: `We deliver to ${area.area} in ${area.deliveryTime}`,
      });
    } else {
      setServiceAvailable(false);
      toast({
        title: "Service Not Available",
        description: "We don't serve this area yet, but we're expanding soon!",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Service Areas
            </h1>
            <p className="text-lg text-muted-foreground">
              Check if we deliver to your area and explore our coverage zones
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <Card className="shadow-lg border-0 mb-8">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <MapPin className="h-5 w-5 mr-2 text-blue-600" />
                    Check Service Availability
                  </CardTitle>
                  <CardDescription>
                    Enter your pincode to check if we deliver to your area
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleCheckService} className="flex gap-4">
                    <div className="flex-1">
                      <Label htmlFor="pincode">Pincode</Label>
                      <Input
                        id="pincode"
                        placeholder="Enter your 6-digit pincode"
                        value={pincode}
                        onChange={(e) => setPincode(e.target.value)}
                        className="h-11 mt-2"
                        maxLength={6}
                        pattern="[0-9]{6}"
                      />
                    </div>
                    <div className="pt-6">
                      <Button type="submit" className="h-11 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                        Check Availability
                      </Button>
                    </div>
                  </form>
                  
                  {serviceAvailable && (
                    <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-semibold text-green-800">Service Available!</h3>
                          <p className="text-green-700">
                            {serviceAvailable.area} - Delivery in {serviceAvailable.deliveryTime}
                          </p>
                        </div>
                        <Badge className="bg-green-100 text-green-800">
                          ✓ Available
                        </Badge>
                      </div>
                    </div>
                  )}
                  
                  {serviceAvailable === false && (
                    <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-semibold text-red-800">Service Not Available</h3>
                          <p className="text-red-700">
                            We don't serve this area yet, but we're expanding soon!
                          </p>
                        </div>
                        <Badge variant="destructive">
                          ✗ Not Available
                        </Badge>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="shadow-lg border-0">
                <CardHeader>
                  <CardTitle>Active Service Areas</CardTitle>
                  <CardDescription>
                    We currently serve these areas in Mumbai
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {serviceAreas.map((area, index) => (
                      <div key={index} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-semibold">{area.area}</h3>
                            <p className="text-sm text-muted-foreground">Pincode: {area.pincode}</p>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            <Clock className="h-3 w-3 mr-1" />
                            {area.deliveryTime}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-6">
              <Card className="shadow-lg border-0">
                <CardHeader>
                  <CardTitle className="text-lg">Service Stats</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600 mb-1">
                      8+
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Active areas in Mumbai
                    </p>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600 mb-1">
                      2-5 hrs
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Average delivery time
                    </p>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple-600 mb-1">
                      98%
                    </div>
                    <p className="text-sm text-muted-foreground">
                      On-time delivery rate
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-lg border-0">
                <CardHeader>
                  <CardTitle className="text-lg">Coming Soon</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Thane</span>
                      <Badge variant="outline">Q1 2024</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Navi Mumbai</span>
                      <Badge variant="outline">Q1 2024</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Pune</span>
                      <Badge variant="outline">Q2 2024</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Delhi NCR</span>
                      <Badge variant="outline">Q2 2024</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-lg border-0">
                <CardHeader>
                  <CardTitle className="text-lg">Request New Area</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    Don't see your area? Let us know where you'd like us to deliver!
                  </p>
                  <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                    Request Service Area
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServiceAreas;
