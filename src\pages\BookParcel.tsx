
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Package } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import ParcelForm from "@/components/ParcelForm";
import CostEstimateCard from "@/components/CostEstimateCard";
import DeliveryInfoCard from "@/components/DeliveryInfoCard";
import LoginPrompt from "@/components/LoginPrompt";

const BookParcel = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [addresses, setAddresses] = useState([]);
  const [estimatedCost, setEstimatedCost] = useState(0);
  const { user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (user) {
      fetchAddresses();
    }
  }, [user]);

  const fetchAddresses = async () => {
    try {
      const { data, error } = await supabase
        .from('addresses')
        .select('*')
        .eq('user_id', user?.id)
        .order('is_default', { ascending: false });

      if (error) {
        console.error('Error fetching addresses:', error);
        toast.error('Failed to load addresses');
        return;
      }

      if (data) {
        setAddresses(data);
      }
    } catch (error) {
      console.error('Unexpected error fetching addresses:', error);
      toast.error('Failed to load addresses');
    }
  };

  const calculateCost = (weight: number, distance = 5) => {
    const baseCost = 50;
    const weightCost = weight * 10;
    const distanceCost = distance * 5;
    return baseCost + weightCost + distanceCost;
  };

  const generateTrackingId = () => {
    return 'QD' + Math.random().toString(36).substr(2, 9).toUpperCase();
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!user) {
      toast.error('Please login first to book a parcel');
      navigate('/auth');
      return;
    }

    setIsLoading(true);

    try {
      const formData = new FormData(e.currentTarget);
      const trackingId = generateTrackingId();
      
      const parcelData = {
        user_id: user.id,
        pickup_address_id: formData.get('pickup_address') as string,
        delivery_address_id: formData.get('delivery_address') as string,
        recipient_name: formData.get('recipient_name') as string,
        recipient_phone: formData.get('recipient_phone') as string,
        parcel_type: formData.get('parcel_type') as string,
        weight: parseFloat(formData.get('weight') as string),
        dimensions: formData.get('dimensions') as string || '',
        special_instructions: formData.get('special_instructions') as string || '',
        estimated_cost: estimatedCost,
        status: 'booked',
        tracking_id: trackingId
      };

      console.log('Submitting parcel data:', parcelData);

      const { data, error } = await supabase
        .from('parcels')
        .insert(parcelData)
        .select()
        .single();

      if (error) {
        console.error('Supabase error:', error);
        toast.error('Failed to book parcel: ' + error.message);
        return;
      }

      if (data) {
        console.log('Parcel booked successfully:', data);
        toast.success(`Parcel booked successfully! Tracking ID: ${data.tracking_id}`);
        navigate('/dashboard');
      }
    } catch (error) {
      console.error('Unexpected error:', error);
      toast.error('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleWeightChange = (weight: number) => {
    setEstimatedCost(calculateCost(weight));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Book a Parcel</h1>
            <p className="text-gray-600">Fast, reliable delivery at your fingertips</p>
          </div>

          {!user ? (
            <LoginPrompt />
          ) : (
            <div className="grid lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Package className="h-5 w-5" />
                      Parcel Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ParcelForm
                      addresses={addresses}
                      isLoading={isLoading}
                      estimatedCost={estimatedCost}
                      onSubmit={handleSubmit}
                      onWeightChange={handleWeightChange}
                    />
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-6">
                <CostEstimateCard estimatedCost={estimatedCost} />
                <DeliveryInfoCard />
              </div>
            </div>
          )}
        </div>
      </div>
      
      <Footer />
    </div>
  );
};

export default BookParcel;
