
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { HelpCircle, MessageSquare, Phone, Mail } from "lucide-react";
import { toast } from "sonner";

const HelpSupport = () => {
  const [supportForm, setSupportForm] = useState({
    subject: '',
    message: ''
  });

  const faqs = [
    {
      question: "How do I track my parcel?",
      answer: "You can track your parcel by entering your tracking ID on the Track Parcel page. Your tracking ID starts with 'QD' followed by 9 digits."
    },
    {
      question: "What are your delivery charges?",
      answer: "Our delivery charges start from ₹50 with additional charges based on weight and distance. You can see the exact cost estimate when booking a parcel."
    },
    {
      question: "How long does delivery take?",
      answer: "We offer same-day delivery, express delivery (2-4 hours), and standard delivery (24 hours) depending on your location and preference."
    },
    {
      question: "Can I change my delivery address?",
      answer: "You can change your delivery address before the parcel is picked up. Contact our support team with your tracking ID for assistance."
    },
    {
      question: "What if my parcel is damaged?",
      answer: "We provide full insurance coverage for all parcels. Report any damage within 24 hours of delivery and we'll process your claim immediately."
    }
  ];

  const handleSupportSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!supportForm.subject || !supportForm.message) {
      toast.error('Please fill in all fields');
      return;
    }
    
    // Here you would typically send the support request to your backend
    toast.success('Support request submitted successfully! We\'ll get back to you within 24 hours.');
    setSupportForm({ subject: '', message: '' });
  };

  return (
    <div className="space-y-6">
      {/* FAQ Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HelpCircle className="h-5 w-5" />
            Frequently Asked Questions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Accordion type="single" collapsible className="w-full">
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`}>
                <AccordionTrigger>{faq.question}</AccordionTrigger>
                <AccordionContent>{faq.answer}</AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </CardContent>
      </Card>

      {/* Contact Support */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Contact Support
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="font-semibold mb-4">Get in Touch</h3>
              <div className="flex items-center gap-3">
                <Phone className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium">Call Us</p>
                  <p className="text-sm text-gray-600">+91 99999 88888</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Mail className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium">Email Us</p>
                  <p className="text-sm text-gray-600"><EMAIL></p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <MessageSquare className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium">Live Chat</p>
                  <p className="text-sm text-gray-600">Available 24/7</p>
                </div>
              </div>
            </div>

            <form onSubmit={handleSupportSubmit} className="space-y-4">
              <h3 className="font-semibold mb-4">Send us a Message</h3>
              <Input
                placeholder="Subject"
                value={supportForm.subject}
                onChange={(e) => setSupportForm(prev => ({ ...prev, subject: e.target.value }))}
              />
              <Textarea
                placeholder="Describe your issue..."
                rows={4}
                value={supportForm.message}
                onChange={(e) => setSupportForm(prev => ({ ...prev, message: e.target.value }))}
              />
              <Button type="submit" className="w-full">
                Send Message
              </Button>
            </form>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default HelpSupport;
