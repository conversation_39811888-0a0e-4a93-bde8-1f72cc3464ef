// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://hanribktibgyjnhxdzbz.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhhbnJpYmt0aWJneWpuaHhkemJ6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkzMTEzOTcsImV4cCI6MjA2NDg4NzM5N30.RG4YoXZB6MUUWuiBCCOm4I-CHTTq6M4lr_Wnum98Hho";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);