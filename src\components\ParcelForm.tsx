
import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Link } from "react-router-dom";

interface ParcelFormProps {
  addresses: any[];
  isLoading: boolean;
  estimatedCost: number;
  onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  onWeightChange: (weight: number) => void;
}

const ParcelForm = ({ 
  addresses, 
  isLoading, 
  estimatedCost, 
  onSubmit, 
  onWeightChange 
}: ParcelFormProps) => {
  const parcelTypes = [
    'Documents',
    'Electronics',
    'Clothing',
    'Books',
    'Food Items',
    'Gifts',
    'Medicine',
    'Others'
  ];

  return (
    <form onSubmit={onSubmit} className="space-y-6">
      {/* Addresses Section */}
      <div className="grid md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label>Pickup Address</Label>
          <Select name="pickup_address" required>
            <SelectTrigger>
              <SelectValue placeholder="Select pickup address" />
            </SelectTrigger>
            <SelectContent>
              {addresses.map((address: any) => (
                <SelectItem key={address.id} value={address.id}>
                  {address.address_line_1}, {address.city}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label>Delivery Address</Label>
          <Select name="delivery_address" required>
            <SelectTrigger>
              <SelectValue placeholder="Select delivery address" />
            </SelectTrigger>
            <SelectContent>
              {addresses.map((address: any) => (
                <SelectItem key={address.id} value={address.id}>
                  {address.address_line_1}, {address.city}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {addresses.length === 0 && (
        <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
          <p className="text-yellow-800 text-sm">
            You need to add addresses first. 
            <Link to="/dashboard" className="text-blue-600 hover:underline ml-1">
              Go to Dashboard to add addresses
            </Link>
          </p>
        </div>
      )}

      {/* Recipient Details */}
      <div className="grid md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="recipient_name">Recipient Name</Label>
          <Input
            id="recipient_name"
            name="recipient_name"
            placeholder="Enter recipient's name"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="recipient_phone">Recipient Phone</Label>
          <Input
            id="recipient_phone"
            name="recipient_phone"
            type="tel"
            placeholder="Enter recipient's phone"
            required
          />
        </div>
      </div>

      {/* Parcel Details */}
      <div className="grid md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label>Parcel Type</Label>
          <Select name="parcel_type" required>
            <SelectTrigger>
              <SelectValue placeholder="Select parcel type" />
            </SelectTrigger>
            <SelectContent>
              {parcelTypes.map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="weight">Weight (kg)</Label>
          <Input
            id="weight"
            name="weight"
            type="number"
            step="0.1"
            placeholder="Enter weight in kg"
            onChange={(e) => onWeightChange(parseFloat(e.target.value) || 0)}
            required
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="dimensions">Dimensions (L x W x H)</Label>
        <Input
          id="dimensions"
          name="dimensions"
          placeholder="e.g., 30 x 20 x 15 cm"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="special_instructions">Special Instructions</Label>
        <Textarea
          id="special_instructions"
          name="special_instructions"
          placeholder="Any special handling instructions..."
          rows={3}
        />
      </div>

      <Button 
        type="submit" 
        className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
        disabled={isLoading || addresses.length === 0}
      >
        {isLoading ? 'Booking Parcel...' : `Book Parcel - ₹${estimatedCost}`}
      </Button>
    </form>
  );
};

export default ParcelForm;
