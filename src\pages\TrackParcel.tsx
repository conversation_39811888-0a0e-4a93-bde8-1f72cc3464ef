
import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Search, Package, MapPin, Clock, CheckCircle, Truck } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const TrackParcel = () => {
  const [trackingId, setTrackingId] = useState('');
  const [parcelData, setParcelData] = useState(null);
  const [trackingEvents, setTrackingEvents] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const handleTrack = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!trackingId.trim()) {
      toast.error('Please enter a tracking ID');
      return;
    }

    setIsLoading(true);

    try {
      // Fetch parcel details
      const { data: parcel, error: parcelError } = await supabase
        .from('parcels')
        .select(`
          *,
          pickup_address:addresses!parcels_pickup_address_id_fkey(*),
          delivery_address:addresses!parcels_delivery_address_id_fkey(*)
        `)
        .eq('tracking_id', trackingId)
        .single();

      if (parcelError) {
        toast.error('Parcel not found. Please check your tracking ID.');
        setParcelData(null);
        setTrackingEvents([]);
        return;
      }

      // Fetch tracking events
      const { data: events, error: eventsError } = await supabase
        .from('tracking_events')
        .select('*')
        .eq('parcel_id', parcel.id)
        .order('created_at', { ascending: false });

      if (!eventsError) {
        setTrackingEvents(events || []);
      }

      setParcelData(parcel);
      toast.success('Parcel found!');
    } catch (error) {
      toast.error('Error tracking parcel');
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered': return 'bg-green-100 text-green-800';
      case 'out_for_delivery': return 'bg-blue-100 text-blue-800';
      case 'in_transit': return 'bg-yellow-100 text-yellow-800';
      case 'picked_up': return 'bg-purple-100 text-purple-800';
      case 'booked': return 'bg-gray-100 text-gray-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'delivered': return <CheckCircle className="h-4 w-4" />;
      case 'out_for_delivery': return <Truck className="h-4 w-4" />;
      case 'in_transit': return <MapPin className="h-4 w-4" />;
      case 'picked_up': return <Package className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Track Your Parcel</h1>
            <p className="text-gray-600">Enter your tracking ID to see real-time updates</p>
          </div>

          {/* Tracking Form */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                Enter Tracking ID
              </CardTitle>
              <CardDescription>
                Your tracking ID starts with "QD" followed by 9 digits
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleTrack} className="flex gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Enter tracking ID (e.g., QD123456789)"
                    value={trackingId}
                    onChange={(e) => setTrackingId(e.target.value)}
                    className="text-lg"
                  />
                </div>
                <Button 
                  type="submit" 
                  disabled={isLoading}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                >
                  {isLoading ? 'Tracking...' : 'Track'}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Parcel Details */}
          {parcelData && (
            <div className="grid lg:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    Parcel Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Status:</span>
                    <Badge className={getStatusColor(parcelData.status)}>
                      <span className="flex items-center gap-1">
                        {getStatusIcon(parcelData.status)}
                        {parcelData.status.replace('_', ' ').toUpperCase()}
                      </span>
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Tracking ID</p>
                      <p className="font-medium">{parcelData.tracking_id}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Parcel Type</p>
                      <p className="font-medium">{parcelData.parcel_type}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Weight</p>
                      <p className="font-medium">{parcelData.weight} kg</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Cost</p>
                      <p className="font-medium">₹{parcelData.estimated_cost}</p>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-gray-500">Recipient</p>
                    <p className="font-medium">{parcelData.recipient_name}</p>
                    <p className="text-sm text-gray-600">{parcelData.recipient_phone}</p>
                  </div>

                  <div>
                    <p className="text-sm text-gray-500">Pickup Address</p>
                    <p className="text-sm">
                      {parcelData.pickup_address?.address_line_1}, {parcelData.pickup_address?.city}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm text-gray-500">Delivery Address</p>
                    <p className="text-sm">
                      {parcelData.delivery_address?.address_line_1}, {parcelData.delivery_address?.city}
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Tracking Timeline */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    Tracking Timeline
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {trackingEvents.length > 0 ? (
                    <div className="space-y-4">
                      {trackingEvents.map((event, index) => (
                        <div key={event.id} className="flex gap-4">
                          <div className="flex flex-col items-center">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                              index === 0 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
                            }`}>
                              {getStatusIcon(event.event_type)}
                            </div>
                            {index < trackingEvents.length - 1 && (
                              <div className="w-0.5 h-8 bg-gray-200 mt-2"></div>
                            )}
                          </div>
                          <div className="flex-1">
                            <p className="font-medium">{event.description}</p>
                            <p className="text-sm text-gray-500">
                              {new Date(event.created_at).toLocaleString()}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Clock className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600">No tracking events yet</p>
                      <p className="text-sm text-gray-500">Updates will appear here as your parcel moves</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          {/* Sample Tracking ID */}
          {!parcelData && (
            <Card>
              <CardContent className="text-center py-8">
                <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Parcel Tracked Yet</h3>
                <p className="text-gray-600 mb-4">
                  Enter your tracking ID above to see your parcel's journey
                </p>
                <p className="text-sm text-gray-500">
                  Don't have a tracking ID? <a href="/book-parcel" className="text-blue-600 hover:underline">Book a parcel</a> to get one.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
      
      <Footer />
    </div>
  );
};

export default TrackParcel;
