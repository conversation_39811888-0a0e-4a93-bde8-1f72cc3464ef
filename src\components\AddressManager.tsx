
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { MapPin, Plus, Edit, Trash2, Star } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

const AddressManager = () => {
  const [addresses, setAddresses] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingAddress, setEditingAddress] = useState(null);
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      fetchAddresses();
    }
  }, [user]);

  const fetchAddresses = async () => {
    const { data, error } = await supabase
      .from('addresses')
      .select('*')
      .eq('user_id', user?.id)
      .order('is_default', { ascending: false });

    if (!error && data) {
      setAddresses(data);
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);

    const formData = new FormData(e.currentTarget);
    const addressData = {
      user_id: user?.id,
      address_line_1: formData.get('address_line_1') as string,
      address_line_2: formData.get('address_line_2') as string,
      city: formData.get('city') as string,
      state: formData.get('state') as string,
      postal_code: formData.get('postal_code') as string,
      landmark: formData.get('landmark') as string,
      is_default: formData.get('is_default') === 'on'
    };

    let result;
    if (editingAddress) {
      result = await supabase
        .from('addresses')
        .update(addressData)
        .eq('id', editingAddress.id);
    } else {
      result = await supabase
        .from('addresses')
        .insert([addressData]);
    }

    if (result.error) {
      toast.error('Failed to save address: ' + result.error.message);
    } else {
      toast.success(editingAddress ? 'Address updated successfully!' : 'Address added successfully!');
      setIsDialogOpen(false);
      setEditingAddress(null);
      fetchAddresses();
      e.currentTarget.reset();
    }
    
    setIsLoading(false);
  };

  const handleDelete = async (addressId: string) => {
    const { error } = await supabase
      .from('addresses')
      .delete()
      .eq('id', addressId);

    if (error) {
      toast.error('Failed to delete address');
    } else {
      toast.success('Address deleted successfully');
      fetchAddresses();
    }
  };

  const handleEdit = (address: any) => {
    setEditingAddress(address);
    setIsDialogOpen(true);
  };

  const handleSetDefault = async (addressId: string) => {
    // First, unset all default addresses
    await supabase
      .from('addresses')
      .update({ is_default: false })
      .eq('user_id', user?.id);

    // Then set the selected address as default
    const { error } = await supabase
      .from('addresses')
      .update({ is_default: true })
      .eq('id', addressId);

    if (error) {
      toast.error('Failed to set default address');
    } else {
      toast.success('Default address updated');
      fetchAddresses();
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Manage Addresses</h2>
          <p className="text-gray-600">Add and manage your pickup and delivery addresses</p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button 
              onClick={() => setEditingAddress(null)}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Address
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>
                {editingAddress ? 'Edit Address' : 'Add New Address'}
              </DialogTitle>
              <DialogDescription>
                {editingAddress ? 'Update your address details' : 'Add a new address for pickup or delivery'}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="address_line_1">Address Line 1 *</Label>
                <Input
                  id="address_line_1"
                  name="address_line_1"
                  placeholder="Street address, building, etc."
                  defaultValue={editingAddress?.address_line_1 || ''}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="address_line_2">Address Line 2</Label>
                <Input
                  id="address_line_2"
                  name="address_line_2"
                  placeholder="Apartment, suite, etc."
                  defaultValue={editingAddress?.address_line_2 || ''}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="city">City *</Label>
                  <Input
                    id="city"
                    name="city"
                    placeholder="City"
                    defaultValue={editingAddress?.city || ''}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="state">State *</Label>
                  <Input
                    id="state"
                    name="state"
                    placeholder="State"
                    defaultValue={editingAddress?.state || ''}
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="postal_code">Postal Code *</Label>
                  <Input
                    id="postal_code"
                    name="postal_code"
                    placeholder="PIN Code"
                    defaultValue={editingAddress?.postal_code || ''}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="landmark">Landmark</Label>
                  <Input
                    id="landmark"
                    name="landmark"
                    placeholder="Nearby landmark"
                    defaultValue={editingAddress?.landmark || ''}
                  />
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="is_default"
                  name="is_default"
                  defaultChecked={editingAddress?.is_default || false}
                  className="rounded"
                />
                <Label htmlFor="is_default">Set as default address</Label>
              </div>
              <Button 
                type="submit" 
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? 'Saving...' : (editingAddress ? 'Update Address' : 'Add Address')}
              </Button>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {addresses.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <MapPin className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Addresses Added</h3>
            <p className="text-gray-600 mb-6">
              Add your first address to start booking parcels
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid md:grid-cols-2 gap-6">
          {addresses.map((address) => (
            <Card key={address.id} className="relative">
              {address.is_default && (
                <Badge className="absolute top-4 right-4 bg-blue-100 text-blue-800">
                  <Star className="h-3 w-3 mr-1" />
                  Default
                </Badge>
              )}
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Address
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="font-medium">{address.address_line_1}</p>
                  {address.address_line_2 && (
                    <p className="text-gray-600">{address.address_line_2}</p>
                  )}
                  <p className="text-gray-600">
                    {address.city}, {address.state} {address.postal_code}
                  </p>
                  {address.landmark && (
                    <p className="text-sm text-gray-500">
                      Near: {address.landmark}
                    </p>
                  )}
                </div>
                <div className="flex gap-2 mt-4">
                  {!address.is_default && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSetDefault(address.id)}
                    >
                      <Star className="h-3 w-3 mr-1" />
                      Set Default
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(address)}
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    Edit
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDelete(address.id)}
                  >
                    <Trash2 className="h-3 w-3 mr-1" />
                    Delete
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default AddressManager;
