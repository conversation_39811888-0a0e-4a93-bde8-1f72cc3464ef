
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Calculator } from "lucide-react";

interface CostEstimateCardProps {
  estimatedCost: number;
}

const CostEstimateCard = ({ estimatedCost }: CostEstimateCardProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calculator className="h-5 w-5" />
          Cost Estimate
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex justify-between">
            <span>Base Cost:</span>
            <span>₹50</span>
          </div>
          <div className="flex justify-between">
            <span>Weight Charges:</span>
            <span>₹{estimatedCost > 50 ? estimatedCost - 75 : 0}</span>
          </div>
          <div className="flex justify-between">
            <span>Distance Charges:</span>
            <span>₹25</span>
          </div>
          <hr />
          <div className="flex justify-between font-bold text-lg">
            <span>Total:</span>
            <span>₹{estimatedCost}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CostEstimateCard;
