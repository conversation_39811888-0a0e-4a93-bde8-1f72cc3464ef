
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Package, 
  Users, 
  Truck, 
  DollarSign, 
  TrendingUp, 
  Clock,
  CheckCircle,
  AlertTriangle
} from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

const AdminOverview = () => {
  const [stats, setStats] = useState({
    totalParcels: 0,
    totalUsers: 0,
    totalPartners: 0,
    totalRevenue: 0,
    pendingParcels: 0,
    deliveredParcels: 0,
    inTransitParcels: 0,
    activeParcels: 0
  });
  const [recentActivity, setRecentActivity] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchStats();
    fetchRecentActivity();
  }, []);

  const fetchStats = async () => {
    try {
      // Fetch parcels stats
      const { data: parcels } = await supabase
        .from('parcels')
        .select('status, estimated_cost');

      // Fetch users count (from profiles since we can't access auth.users directly)
      const { data: profiles } = await supabase
        .from('profiles')
        .select('id');

      // Fetch partners count
      const { data: partners } = await supabase
        .from('delivery_partners')
        .select('id, status');

      if (parcels) {
        const totalParcels = parcels.length;
        const pendingParcels = parcels.filter(p => p.status === 'booked').length;
        const deliveredParcels = parcels.filter(p => p.status === 'delivered').length;
        const inTransitParcels = parcels.filter(p => ['picked_up', 'in_transit'].includes(p.status)).length;
        const activeParcels = parcels.filter(p => p.status !== 'delivered' && p.status !== 'cancelled').length;
        const totalRevenue = parcels.reduce((sum, p) => sum + (p.estimated_cost || 0), 0);

        setStats({
          totalParcels,
          totalUsers: profiles?.length || 0,
          totalPartners: partners?.length || 0,
          totalRevenue,
          pendingParcels,
          deliveredParcels,
          inTransitParcels,
          activeParcels
        });
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchRecentActivity = async () => {
    try {
      const { data: parcels } = await supabase
        .from('parcels')
        .select('*, profiles(full_name)')
        .order('created_at', { ascending: false })
        .limit(10);

      if (parcels) {
        setRecentActivity(parcels);
      }
    } catch (error) {
      console.error('Error fetching recent activity:', error);
    }
  };

  const statCards = [
    {
      title: "Total Parcels",
      value: stats.totalParcels,
      icon: Package,
      color: "bg-blue-500",
      change: "+12%"
    },
    {
      title: "Total Users",
      value: stats.totalUsers,
      icon: Users,
      color: "bg-green-500",
      change: "+8%"
    },
    {
      title: "Active Partners",
      value: stats.totalPartners,
      icon: Truck,
      color: "bg-purple-500",
      change: "+15%"
    },
    {
      title: "Total Revenue",
      value: `₹${stats.totalRevenue.toLocaleString()}`,
      icon: DollarSign,
      color: "bg-yellow-500",
      change: "+23%"
    }
  ];

  const statusCards = [
    {
      title: "Pending Parcels",
      value: stats.pendingParcels,
      icon: Clock,
      color: "text-yellow-600",
      bgColor: "bg-yellow-100"
    },
    {
      title: "In Transit",
      value: stats.inTransitParcels,
      icon: TrendingUp,
      color: "text-blue-600",
      bgColor: "bg-blue-100"
    },
    {
      title: "Delivered",
      value: stats.deliveredParcels,
      icon: CheckCircle,
      color: "text-green-600",
      bgColor: "bg-green-100"
    },
    {
      title: "Active Parcels",
      value: stats.activeParcels,
      icon: AlertTriangle,
      color: "text-orange-600",
      bgColor: "bg-orange-100"
    }
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-20 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  <p className="text-sm text-green-600 font-medium">{stat.change} from last month</p>
                </div>
                <div className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statusCards.map((status, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{status.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{status.value}</p>
                </div>
                <div className={`w-10 h-10 ${status.bgColor} rounded-lg flex items-center justify-center`}>
                  <status.icon className={`h-5 w-5 ${status.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentActivity.slice(0, 5).map((parcel: any) => (
              <div key={parcel.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Package className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="font-medium">Parcel {parcel.tracking_id}</p>
                    <p className="text-sm text-gray-600">
                      {parcel.profiles?.full_name || 'Unknown User'} • {parcel.parcel_type}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <Badge 
                    variant={parcel.status === 'delivered' ? 'default' : 'secondary'}
                    className={parcel.status === 'delivered' ? 'bg-green-500' : ''}
                  >
                    {parcel.status}
                  </Badge>
                  <p className="text-sm text-gray-600 mt-1">₹{parcel.estimated_cost}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminOverview;
