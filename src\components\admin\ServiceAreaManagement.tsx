
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { MapPin, Search, Plus, Edit, Trash2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

const ServiceAreaManagement = () => {
  const [areas, setAreas] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchServiceAreas();
  }, []);

  const fetchServiceAreas = async () => {
    try {
      const { data, error } = await supabase
        .from('service_areas')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching service areas:', error);
        toast.error('Failed to load service areas');
        return;
      }

      if (data) {
        setAreas(data);
      }
    } catch (error) {
      console.error('Unexpected error:', error);
      toast.error('Failed to load service areas');
    } finally {
      setLoading(false);
    }
  };

  const toggleAreaStatus = async (areaId: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('service_areas')
        .update({ is_active: !currentStatus })
        .eq('id', areaId);

      if (error) {
        toast.error('Failed to update service area status');
        return;
      }

      toast.success('Service area status updated successfully');
      fetchServiceAreas();
    } catch (error) {
      console.error('Error updating service area:', error);
      toast.error('Failed to update service area status');
    }
  };

  const filteredAreas = areas.filter((area: any) => {
    return area.area_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
           area.city?.toLowerCase().includes(searchTerm.toLowerCase()) ||
           area.state?.toLowerCase().includes(searchTerm.toLowerCase());
  });

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Service Area Management
            </CardTitle>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Service Area
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Search */}
          <div className="relative mb-6">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search by area name, city, or state..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Service Areas Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Area Name</TableHead>
                  <TableHead>City</TableHead>
                  <TableHead>State</TableHead>
                  <TableHead>Postal Codes</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAreas.map((area: any) => (
                  <TableRow key={area.id}>
                    <TableCell className="font-medium">{area.area_name}</TableCell>
                    <TableCell>{area.city}</TableCell>
                    <TableCell>{area.state}</TableCell>
                    <TableCell>
                      <div className="max-w-xs">
                        {area.postal_codes?.slice(0, 3).join(', ')}
                        {area.postal_codes?.length > 3 && ` +${area.postal_codes.length - 3} more`}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant={area.is_active ? 'default' : 'secondary'}
                        className={area.is_active ? 'bg-green-500' : 'bg-gray-500'}
                      >
                        {area.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {new Date(area.created_at).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => toggleAreaStatus(area.id, area.is_active)}
                        >
                          {area.is_active ? 'Deactivate' : 'Activate'}
                        </Button>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {filteredAreas.length === 0 && (
              <div className="text-center py-8">
                <MapPin className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No service areas found</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ServiceAreaManagement;
