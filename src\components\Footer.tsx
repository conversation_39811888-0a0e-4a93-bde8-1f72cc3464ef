
import { Package, MapPin, Truck, User } from "lucide-react";
import { Link } from "react-router-dom";

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <Package className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold">QuickDelivery</span>
            </div>
            <p className="text-gray-400 text-sm">
              Your trusted hyperlocal delivery partner. Fast, reliable, and secure parcel delivery across the city.
            </p>
          </div>
          
          <div>
            <h3 className="font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li><Link to="/book-parcel" className="text-gray-400 hover:text-white transition-colors">Book Delivery</Link></li>
              <li><Link to="/track-parcel" className="text-gray-400 hover:text-white transition-colors">Track Parcel</Link></li>
              <li><Link to="/service-areas" className="text-gray-400 hover:text-white transition-colors">Service Areas</Link></li>
              <li><Link to="/mobile-app" className="text-gray-400 hover:text-white transition-colors">Mobile App</Link></li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold mb-4">For Partners</h3>
            <ul className="space-y-2">
              <li><Link to="/partner-register" className="text-gray-400 hover:text-white transition-colors">Become a Partner</Link></li>
              <li><Link to="/dashboard" className="text-gray-400 hover:text-white transition-colors">Partner Dashboard</Link></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Earnings</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Support</a></li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold mb-4">Contact</h3>
            <ul className="space-y-2 text-gray-400 text-sm">
              <li>📞 +91 98765 43210</li>
              <li>📧 <EMAIL></li>
              <li>📍 Mumbai, Maharashtra</li>
              <li>🕒 24/7 Customer Support</li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400 text-sm">
          <p>&copy; 2024 QuickDelivery. All rights reserved. | Privacy Policy | Terms of Service</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
