import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Package, MapPin, User, Plus, History, Truck, Star, HelpCircle } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import AddressManager from "@/components/AddressManager";
import StatsCards from "@/components/StatsCards";
import NotificationsPanel from "@/components/NotificationsPanel";
import QuickActions from "@/components/QuickActions";
import HelpSupport from "@/components/HelpSupport";

const Dashboard = () => {
  const [parcels, setParcels] = useState([]);
  const [profile, setProfile] = useState(null);
  const [partnerProfile, setPartnerProfile] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!user) {
      navigate('/auth');
      return;
    }
    fetchDashboardData();
  }, [user]);

  const fetchDashboardData = async () => {
    try {
      // ... keep existing code (profile and partner data fetching)
      const { data: profileData } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (profileData) {
        setProfile(profileData);
      }

      const { data: partnerData } = await supabase
        .from('delivery_partners')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (partnerData) {
        setPartnerProfile(partnerData);
      }

      const { data: parcelsData } = await supabase
        .from('parcels')
        .select(`
          *,
          pickup_address:addresses!parcels_pickup_address_id_fkey(*),
          delivery_address:addresses!parcels_delivery_address_id_fkey(*)
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (parcelsData) {
        setParcels(parcelsData);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered': return 'bg-green-100 text-green-800';
      case 'out_for_delivery': return 'bg-blue-100 text-blue-800';
      case 'in_transit': return 'bg-yellow-100 text-yellow-800';
      case 'picked_up': return 'bg-purple-100 text-purple-800';
      case 'booked': return 'bg-gray-100 text-gray-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Calculate stats
  const totalParcels = parcels.length;
  const deliveredParcels = parcels.filter(p => p.status === 'delivered').length;
  const inTransitParcels = parcels.filter(p => ['in_transit', 'picked_up', 'out_for_delivery'].includes(p.status)).length;
  const totalSpent = parcels.reduce((sum, p) => sum + (p.estimated_cost || 0), 0);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
              <p className="text-gray-600">Welcome back, {profile?.full_name || user?.email}</p>
            </div>
            <Button asChild className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
              <Link to="/book-parcel">
                <Plus className="h-4 w-4 mr-2" />
                Book New Parcel
              </Link>
            </Button>
          </div>

          {/* Stats Cards */}
          <StatsCards 
            totalParcels={totalParcels}
            deliveredParcels={deliveredParcels}
            inTransitParcels={inTransitParcels}
            totalSpent={totalSpent}
          />

          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="parcels">My Parcels</TabsTrigger>
              <TabsTrigger value="addresses">Addresses</TabsTrigger>
              <TabsTrigger value="profile">Profile</TabsTrigger>
              <TabsTrigger value="partner">
                {partnerProfile ? 'Partner' : 'Become Partner'}
              </TabsTrigger>
              <TabsTrigger value="help">Help & Support</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              <div className="grid lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2 space-y-6">
                  <NotificationsPanel />
                </div>
                <div>
                  <QuickActions />
                </div>
              </div>
            </TabsContent>

            {/* Parcels Tab */}
            <TabsContent value="parcels" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Parcels</CardTitle>
                  <CardDescription>Your latest parcel bookings</CardDescription>
                </CardHeader>
                <CardContent>
                  {parcels.length === 0 ? (
                    <div className="text-center py-8">
                      <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No Parcels Yet</h3>
                      <p className="text-gray-600 mb-4">You haven't booked any parcels yet.</p>
                      <Button asChild>
                        <Link to="/book-parcel">Book Your First Parcel</Link>
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {parcels.slice(0, 10).map((parcel) => (
                        <div key={parcel.id} className="flex items-center justify-between p-4 border rounded-lg hover:shadow-md transition-shadow">
                          <div className="flex-1">
                            <div className="flex items-center gap-4">
                              <div>
                                <p className="font-medium">{parcel.tracking_id}</p>
                                <p className="text-sm text-gray-600">
                                  {parcel.pickup_address?.city} → {parcel.delivery_address?.city}
                                </p>
                                <p className="text-xs text-gray-500">
                                  {new Date(parcel.created_at).toLocaleDateString()}
                                </p>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-4">
                            <Badge className={getStatusColor(parcel.status)}>
                              {parcel.status.replace('_', ' ').toUpperCase()}
                            </Badge>
                            <span className="font-medium">₹{parcel.estimated_cost}</span>
                            <Button variant="outline" size="sm" asChild>
                              <Link to={`/track-parcel?id=${parcel.tracking_id}`}>Track</Link>
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Addresses Tab */}
            <TabsContent value="addresses">
              <AddressManager />
            </TabsContent>

            {/* Profile Tab */}
            <TabsContent value="profile">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Profile Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-500">Full Name</p>
                        <p className="font-medium">{profile?.full_name || 'Not set'}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Email</p>
                        <p className="font-medium">{profile?.email || user?.email}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Phone</p>
                        <p className="font-medium">{profile?.phone || 'Not set'}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Member Since</p>
                        <p className="font-medium">
                          {new Date(profile?.created_at || user?.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Partner Tab */}
            <TabsContent value="partner">
              {partnerProfile ? (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Truck className="h-5 w-5" />
                      Partner Dashboard
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div>
                          <p className="text-sm text-gray-500">Status</p>
                          <Badge className={
                            partnerProfile.status === 'approved' ? 'bg-green-100 text-green-800' :
                            partnerProfile.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }>
                            {partnerProfile.status.toUpperCase()}
                          </Badge>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Vehicle Type</p>
                          <p className="font-medium">{partnerProfile.vehicle_type}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Vehicle Number</p>
                          <p className="font-medium">{partnerProfile.vehicle_number}</p>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div>
                          <p className="text-sm text-gray-500">Rating</p>
                          <div className="flex items-center gap-2">
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            <span className="font-medium">{partnerProfile.rating}/5.0</span>
                          </div>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Total Deliveries</p>
                          <p className="font-medium">{partnerProfile.total_deliveries}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Availability</p>
                          <Badge className={partnerProfile.is_available ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                            {partnerProfile.is_available ? 'Available' : 'Offline'}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardContent className="text-center py-12">
                    <Truck className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Become a Delivery Partner</h3>
                    <p className="text-gray-600 mb-6">
                      Join our network of delivery partners and start earning today
                    </p>
                    <Button asChild className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                      <Link to="/partner-register">Apply Now</Link>
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Help & Support Tab */}
            <TabsContent value="help">
              <HelpSupport />
            </TabsContent>
          </Tabs>
        </div>
      </div>
      
      <Footer />
    </div>
  );
};

export default Dashboard;
