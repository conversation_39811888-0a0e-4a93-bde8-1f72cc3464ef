
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Settings, Save, RefreshCw, Database, Mail, Bell } from "lucide-react";
import { toast } from "sonner";

const SystemSettings = () => {
  const [settings, setSettings] = useState({
    siteName: 'QuickDelivery',
    supportEmail: '<EMAIL>',
    baseCost: 50,
    weightCostPerKg: 10,
    distanceCostPerKm: 5,
    enableNotifications: true,
    enableEmailAlerts: true,
    maintenanceMode: false,
    autoAssignPartners: true,
    maxDeliveryRadius: 25
  });

  const [loading, setLoading] = useState(false);

  const handleSaveSettings = async () => {
    setLoading(true);
    try {
      // Simulate saving settings
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success('Settings saved successfully');
    } catch (error) {
      toast.error('Failed to save settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            System Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* General Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">General Settings</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="siteName">Site Name</Label>
                <Input
                  id="siteName"
                  value={settings.siteName}
                  onChange={(e) => handleSettingChange('siteName', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="supportEmail">Support Email</Label>
                <Input
                  id="supportEmail"
                  type="email"
                  value={settings.supportEmail}
                  onChange={(e) => handleSettingChange('supportEmail', e.target.value)}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Pricing Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Pricing Configuration</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="baseCost">Base Cost (₹)</Label>
                <Input
                  id="baseCost"
                  type="number"
                  value={settings.baseCost}
                  onChange={(e) => handleSettingChange('baseCost', parseFloat(e.target.value))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="weightCost">Cost per KG (₹)</Label>
                <Input
                  id="weightCost"
                  type="number"
                  value={settings.weightCostPerKg}
                  onChange={(e) => handleSettingChange('weightCostPerKg', parseFloat(e.target.value))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="distanceCost">Cost per KM (₹)</Label>
                <Input
                  id="distanceCost"
                  type="number"
                  value={settings.distanceCostPerKm}
                  onChange={(e) => handleSettingChange('distanceCostPerKm', parseFloat(e.target.value))}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Operation Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Operation Settings</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Enable Notifications</Label>
                  <p className="text-sm text-gray-600">Send push notifications to users</p>
                </div>
                <Switch
                  checked={settings.enableNotifications}
                  onCheckedChange={(checked) => handleSettingChange('enableNotifications', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Enable Email Alerts</Label>
                  <p className="text-sm text-gray-600">Send email notifications for important events</p>
                </div>
                <Switch
                  checked={settings.enableEmailAlerts}
                  onCheckedChange={(checked) => handleSettingChange('enableEmailAlerts', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Auto-assign Partners</Label>
                  <p className="text-sm text-gray-600">Automatically assign delivery partners to new parcels</p>
                </div>
                <Switch
                  checked={settings.autoAssignPartners}
                  onCheckedChange={(checked) => handleSettingChange('autoAssignPartners', checked)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxRadius">Maximum Delivery Radius (KM)</Label>
                <Input
                  id="maxRadius"
                  type="number"
                  value={settings.maxDeliveryRadius}
                  onChange={(e) => handleSettingChange('maxDeliveryRadius', parseFloat(e.target.value))}
                  className="w-full md:w-48"
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* System Controls */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">System Controls</h3>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Maintenance Mode</Label>
                <p className="text-sm text-gray-600">Put the system in maintenance mode</p>
              </div>
              <Switch
                checked={settings.maintenanceMode}
                onCheckedChange={(checked) => handleSettingChange('maintenanceMode', checked)}
              />
            </div>
          </div>

          <Separator />

          {/* Action Buttons */}
          <div className="flex gap-4">
            <Button 
              onClick={handleSaveSettings}
              disabled={loading}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              {loading ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Save Settings
            </Button>
            
            <Button variant="outline">
              <Database className="h-4 w-4 mr-2" />
              Backup Database
            </Button>
            
            <Button variant="outline">
              <Mail className="h-4 w-4 mr-2" />
              Test Email
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SystemSettings;
