
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Clock } from "lucide-react";

const DeliveryInfoCard = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Delivery Info
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2 text-sm">
          <p>• Same day delivery available</p>
          <p>• Express delivery in 2-4 hours</p>
          <p>• Standard delivery in 24 hours</p>
          <p>• Real-time tracking included</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default DeliveryInfoCard;
