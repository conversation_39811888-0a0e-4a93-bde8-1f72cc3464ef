
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Package, Search, Filter, Eye, Edit, Trash2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

const ParcelManagement = () => {
  const [parcels, setParcels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  useEffect(() => {
    fetchParcels();
  }, []);

  const fetchParcels = async () => {
    try {
      const { data, error } = await supabase
        .from('parcels')
        .select(`
          *,
          profiles(full_name),
          delivery_partners(id)
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching parcels:', error);
        toast.error('Failed to load parcels');
        return;
      }

      if (data) {
        setParcels(data);
      }
    } catch (error) {
      console.error('Unexpected error:', error);
      toast.error('Failed to load parcels');
    } finally {
      setLoading(false);
    }
  };

  const updateParcelStatus = async (parcelId: string, newStatus: string) => {
    try {
      const { error } = await supabase
        .from('parcels')
        .update({ status: newStatus, updated_at: new Date().toISOString() })
        .eq('id', parcelId);

      if (error) {
        toast.error('Failed to update parcel status');
        return;
      }

      toast.success('Parcel status updated successfully');
      fetchParcels();
    } catch (error) {
      console.error('Error updating parcel:', error);
      toast.error('Failed to update parcel status');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusColors = {
      'booked': 'bg-blue-500',
      'picked_up': 'bg-yellow-500',
      'in_transit': 'bg-orange-500',
      'delivered': 'bg-green-500',
      'cancelled': 'bg-red-500'
    };

    return (
      <Badge className={`${statusColors[status as keyof typeof statusColors] || 'bg-gray-500'} text-white`}>
        {status.replace('_', ' ')}
      </Badge>
    );
  };

  const filteredParcels = parcels.filter((parcel: any) => {
    const matchesSearch = parcel.tracking_id?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         parcel.recipient_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         parcel.profiles?.full_name?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || parcel.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Parcel Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by tracking ID, recipient, or sender..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="booked">Booked</SelectItem>
                <SelectItem value="picked_up">Picked Up</SelectItem>
                <SelectItem value="in_transit">In Transit</SelectItem>
                <SelectItem value="delivered">Delivered</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Parcels Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tracking ID</TableHead>
                  <TableHead>Sender</TableHead>
                  <TableHead>Recipient</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Cost</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredParcels.map((parcel: any) => (
                  <TableRow key={parcel.id}>
                    <TableCell className="font-medium">{parcel.tracking_id}</TableCell>
                    <TableCell>{parcel.profiles?.full_name || 'Unknown'}</TableCell>
                    <TableCell>{parcel.recipient_name}</TableCell>
                    <TableCell>{parcel.parcel_type}</TableCell>
                    <TableCell>{getStatusBadge(parcel.status)}</TableCell>
                    <TableCell>₹{parcel.estimated_cost}</TableCell>
                    <TableCell>{new Date(parcel.created_at).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Select
                          value={parcel.status}
                          onValueChange={(value) => updateParcelStatus(parcel.id, value)}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="booked">Booked</SelectItem>
                            <SelectItem value="picked_up">Picked Up</SelectItem>
                            <SelectItem value="in_transit">In Transit</SelectItem>
                            <SelectItem value="delivered">Delivered</SelectItem>
                            <SelectItem value="cancelled">Cancelled</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {filteredParcels.length === 0 && (
              <div className="text-center py-8">
                <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No parcels found</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ParcelManagement;
